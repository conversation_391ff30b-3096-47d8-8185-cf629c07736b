<svg width="250" height="250" viewBox="0 0 250 250" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients for Creative Director Avatar -->
    <linearGradient id="creativeSkinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0891b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0e7490;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="creativeHairGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#164e63;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0c4a6e;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="creativeClothingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ec4899;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#db2777;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#be185d;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="creativeGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#0891b2;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background glow -->
  <circle cx="125" cy="125" r="120" fill="url(#creativeGlow)" opacity="0.6">
    <animate attributeName="opacity" values="0.4;0.8;0.4" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Orbital rings -->
  <circle cx="125" cy="125" r="108" fill="none" stroke="#06b6d4" stroke-width="1" opacity="0.3">
    <animateTransform attributeName="transform" type="rotate" values="0 125 125;360 125 125" dur="22s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Body/Creative Shirt -->
  <ellipse cx="125" cy="200" rx="48" ry="38" fill="url(#creativeClothingGradient)"/>
  
  <!-- Creative pattern on shirt -->
  <circle cx="115" cy="190" r="3" fill="#06b6d4" opacity="0.7">
    <animate attributeName="r" values="2;4;2" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="135" cy="195" r="2" fill="#ec4899" opacity="0.8">
    <animate attributeName="r" values="1;3;1" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Neck -->
  <rect x="115" y="165" width="20" height="25" fill="url(#creativeSkinGradient)" rx="10"/>
  
  <!-- Head -->
  <circle cx="125" cy="140" r="35" fill="url(#creativeSkinGradient)"/>
  
  <!-- Hair (creative/artistic style) -->
  <path d="M 92 128 Q 125 98 158 128 Q 152 118 125 108 Q 98 118 92 128" fill="url(#creativeHairGradient)"/>
  <path d="M 105 125 Q 125 120 145 125" fill="url(#creativeHairGradient)" opacity="0.8"/>
  
  <!-- Eyes (creative spark) -->
  <circle cx="115" cy="135" r="3" fill="#1f2937"/>
  <circle cx="135" cy="135" r="3" fill="#1f2937"/>
  <circle cx="116" cy="134" r="1" fill="#06b6d4">
    <animate attributeName="fill" values="#06b6d4;#ec4899;#06b6d4" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="136" cy="134" r="1" fill="#06b6d4">
    <animate attributeName="fill" values="#06b6d4;#ec4899;#06b6d4" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Nose -->
  <ellipse cx="125" cy="145" rx="2" ry="4" fill="#0e7490" opacity="0.6"/>
  
  <!-- Mouth -->
  <path d="M 120 155 Q 125 160 130 155" stroke="#1f2937" stroke-width="2" fill="none" stroke-linecap="round"/>
  
  <!-- Creative tools/symbols -->
  <path d="M 145 175 L 155 185 M 155 175 L 145 185" stroke="#06b6d4" stroke-width="2" opacity="0.8"/>
  <circle cx="150" cy="180" r="8" fill="none" stroke="#ec4899" stroke-width="1" opacity="0.7"/>
  
  <!-- Design elements -->
  <polygon points="95,185 100,175 105,185" fill="#06b6d4" opacity="0.7">
    <animateTransform attributeName="transform" type="rotate" values="0 100 180;360 100 180" dur="8s" repeatCount="indefinite"/>
  </polygon>
  
  <!-- Floating creative particles -->
  <circle cx="80" cy="115" r="2" fill="#ec4899" opacity="0.8">
    <animateTransform attributeName="transform" type="translate" values="0,0;20,-20;0,0" dur="5s" repeatCount="indefinite"/>
  </circle>
  <rect x="165" y="125" width="4" height="4" fill="#06b6d4" opacity="0.6" transform="rotate(45 167 127)">
    <animateTransform attributeName="transform" type="translate" values="0,0;-15,15;0,0" dur="6s" repeatCount="indefinite"/>
  </rect>
  
  <!-- Creativity badge -->
  <circle cx="110" cy="175" r="6" fill="#ec4899" opacity="0.8"/>
  <path d="M 107 175 L 109 177 L 113 173" stroke="#ffffff" stroke-width="1" fill="none"/>
</svg>
