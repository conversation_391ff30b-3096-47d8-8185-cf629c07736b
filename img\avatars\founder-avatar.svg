<svg width="250" height="250" viewBox="0 0 250 250" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients for Founder Avatar -->
    <linearGradient id="founderSkinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a855f7;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#9333ea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="founderHairGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4c1d95;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#581c87;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="founderClothingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#374151;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4b5563;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="founderGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#a855f7;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#9333ea;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background glow -->
  <circle cx="125" cy="125" r="120" fill="url(#founderGlow)" opacity="0.6">
    <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Orbital rings -->
  <circle cx="125" cy="125" r="105" fill="none" stroke="#a855f7" stroke-width="1" opacity="0.4">
    <animateTransform attributeName="transform" type="rotate" values="360 125 125;0 125 125" dur="18s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Body/Hoodie -->
  <ellipse cx="125" cy="200" rx="50" ry="40" fill="url(#founderClothingGradient)"/>
  
  <!-- Hoodie details -->
  <path d="M 85 180 Q 125 160 165 180 L 160 200 Q 125 185 90 200 Z" fill="url(#founderClothingGradient)" opacity="0.8"/>
  
  <!-- Neck -->
  <rect x="115" y="165" width="20" height="25" fill="url(#founderSkinGradient)" rx="10"/>
  
  <!-- Head -->
  <circle cx="125" cy="140" r="35" fill="url(#founderSkinGradient)"/>
  
  <!-- Hair (more modern/tech style) -->
  <path d="M 95 125 Q 125 100 155 125 Q 150 115 125 110 Q 100 115 95 125" fill="url(#founderHairGradient)"/>
  <path d="M 110 120 Q 125 115 140 120" fill="url(#founderHairGradient)" opacity="0.7"/>
  
  <!-- Eyes (with tech glow) -->
  <circle cx="115" cy="135" r="3" fill="#1f2937"/>
  <circle cx="135" cy="135" r="3" fill="#1f2937"/>
  <circle cx="116" cy="134" r="1" fill="#a855f7">
    <animate attributeName="fill" values="#a855f7;#ffffff;#a855f7" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="136" cy="134" r="1" fill="#a855f7">
    <animate attributeName="fill" values="#a855f7;#ffffff;#a855f7" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Nose -->
  <ellipse cx="125" cy="145" rx="2" ry="4" fill="#7c3aed" opacity="0.6"/>
  
  <!-- Mouth -->
  <path d="M 120 155 Q 125 160 130 155" stroke="#1f2937" stroke-width="2" fill="none" stroke-linecap="round"/>
  
  <!-- Tech/Code symbols -->
  <text x="105" y="190" font-family="monospace" font-size="8" fill="#a855f7" opacity="0.8">&lt;/&gt;</text>
  
  <!-- Developer badge -->
  <rect x="140" y="175" width="15" height="8" fill="#a855f7" rx="2" opacity="0.8"/>
  <text x="142" y="181" font-family="monospace" font-size="4" fill="#ffffff">DEV</text>
  
  <!-- Floating code particles -->
  <text x="75" y="110" font-family="monospace" font-size="6" fill="#a855f7" opacity="0.6">{}</text>
  <animateTransform attributeName="transform" type="translate" values="0,0;15,-15;0,0" dur="6s" repeatCount="indefinite"/>
  
  <text x="165" y="130" font-family="monospace" font-size="5" fill="#9333ea" opacity="0.7">[]</text>
  <animateTransform attributeName="transform" type="translate" values="0,0;-10,10;0,0" dur="4.5s" repeatCount="indefinite"/>
</svg>
