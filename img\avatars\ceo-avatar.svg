<svg width="250" height="250" viewBox="0 0 250 250" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients for CEO <PERSON><PERSON> -->
    <linearGradient id="ceoSkinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="ceoHairGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#111827;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="ceoSuitGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1d4ed8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="ceoGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:0" />
    </radialGradient>
    
    <!-- Animation definitions -->
    <animateTransform id="ceoRotate" attributeName="transform" type="rotate" 
                      values="0 125 125;360 125 125" dur="20s" repeatCount="indefinite"/>
  </defs>
  
  <!-- Background glow -->
  <circle cx="125" cy="125" r="120" fill="url(#ceoGlow)" opacity="0.6">
    <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Orbital rings -->
  <circle cx="125" cy="125" r="110" fill="none" stroke="#fbbf24" stroke-width="1" opacity="0.3">
    <animateTransform attributeName="transform" type="rotate" values="0 125 125;360 125 125" dur="15s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Body/Suit -->
  <ellipse cx="125" cy="200" rx="45" ry="35" fill="url(#ceoSuitGradient)"/>
  
  <!-- Neck -->
  <rect x="115" y="165" width="20" height="25" fill="url(#ceoSkinGradient)" rx="10"/>
  
  <!-- Head -->
  <circle cx="125" cy="140" r="35" fill="url(#ceoSkinGradient)"/>
  
  <!-- Hair -->
  <path d="M 90 125 Q 125 95 160 125 Q 155 110 125 105 Q 95 110 90 125" fill="url(#ceoHairGradient)"/>
  
  <!-- Eyes -->
  <circle cx="115" cy="135" r="3" fill="#1f2937"/>
  <circle cx="135" cy="135" r="3" fill="#1f2937"/>
  <circle cx="116" cy="134" r="1" fill="#ffffff"/>
  <circle cx="136" cy="134" r="1" fill="#ffffff"/>
  
  <!-- Nose -->
  <ellipse cx="125" cy="145" rx="2" ry="4" fill="#d97706" opacity="0.6"/>
  
  <!-- Mouth -->
  <path d="M 120 155 Q 125 160 130 155" stroke="#1f2937" stroke-width="2" fill="none" stroke-linecap="round"/>
  
  <!-- Suit details -->
  <rect x="120" y="180" width="10" height="20" fill="#1d4ed8"/>
  <circle cx="125" cy="185" r="2" fill="#fbbf24"/>
  
  <!-- CEO Crown/Leadership symbol -->
  <path d="M 115 115 L 125 105 L 135 115 L 130 120 L 120 120 Z" fill="#fbbf24" opacity="0.8">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
  </path>
  
  <!-- Floating particles -->
  <circle cx="80" cy="100" r="2" fill="#fbbf24" opacity="0.7">
    <animateTransform attributeName="transform" type="translate" values="0,0;10,-10;0,0" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="170" cy="120" r="1.5" fill="#f59e0b" opacity="0.6">
    <animateTransform attributeName="transform" type="translate" values="0,0;-8,8;0,0" dur="5s" repeatCount="indefinite"/>
  </circle>
</svg>
